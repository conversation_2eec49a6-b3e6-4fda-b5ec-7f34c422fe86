# Dependencies
node_modules/
*/node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Package lock files (choose one approach)
# package-lock.json  # Needed for GitHub Actions deployment
yarn.lock

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Grunt intermediate storage
.grunt

# Bower dependency directory
bower_components

# node-waf configuration
.lock-wscript

# Compiled binary addons
build/Release

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# parcel-bundler cache
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
# public  # Commented out - needed for React public directory

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# React build output
web/build/
build/

# React Native / Expo
.expo/
.expo-shared/
expo-env.d.ts

# Metro bundler cache
.metro-cache/

# iOS
ios/Pods/
ios/build/
ios/*.xcworkspace/xcuserdata/
ios/*.xcodeproj/xcuserdata/
ios/*.xcodeproj/project.xcworkspace/xcuserdata/
ios/Podfile.lock

# Android
android/app/build/
android/build/
android/.gradle/
android/local.properties
android/app/src/main/assets/fonts/
android/app/src/main/res/drawable-*/

# Fastlane
fastlane/report.xml
fastlane/Preview.html
fastlane/screenshots
fastlane/test_output

# Bundle artifacts
*.jsbundle

# CocoaPods
ios/Pods/
*.xcworkspace

# Expo
.expo/
dist/
web-build/

# EAS Build
.eas/
*.keystore
*.jks
service-account-key.json

# Build outputs
*.apk
*.aab
*.ipa

# Debugging
npm-debug.*
yarn-debug.*
yarn-error.*

# Local configuration files
.vscode/
.idea/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed

# Coverage
lib-cov
coverage

# Grunt
.grunt

# Dependency directory
node_modules

# Users Environment Variables
.lock-wscript

# IDEs and editors (general)
/.vscode
/.idea
.project
.classpath
*.launch
.settings/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Vim
*.swp
*.swo

# OSX
.DS_Store
.AppleDouble
.LSOverride
Icon

# Windows
Thumbs.db
ehthumbs.db

# Linux
*~

# Firebase
.firebase/
firebase-debug.log

# Sentry
.sentryclirc

# Temporary files
*.tmp
*.temp