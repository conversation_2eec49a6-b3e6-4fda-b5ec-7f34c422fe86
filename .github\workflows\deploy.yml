name: Deploy Web App to GitHub Pages

on:
  push:
    branches: [ main ]
    paths:
      - 'web/**'
      - 'shared/**'
      - '.github/workflows/deploy.yml'
  pull_request:
    branches: [ main ]
    paths:
      - 'web/**'
      - 'shared/**'

permissions:
  contents: read
  pages: write
  id-token: write

concurrency:
  group: "pages"
  cancel-in-progress: false

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      
    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        
    - name: Cache dependencies
      uses: actions/cache@v3
      with:
        path: web/node_modules
        key: ${{ runner.os }}-node-${{ hashFiles('web/package-lock.json') }}
        restore-keys: |
          ${{ runner.os }}-node-
        
    - name: Verify package-lock.json
      run: |
        if [ ! -f web/package-lock.json ]; then
          echo "Error: package-lock.json is missing."
          exit 1
        fi
        echo "package-lock.json found"
        
    - name: Install dependencies
      run: |
        cd web
        npm ci
        
    - name: Build
      run: |
        cd web
        npm run build
        
    - name: Setup Pages
      uses: actions/configure-pages@v4
      
    - name: Upload artifact
      uses: actions/upload-pages-artifact@v3
      with:
        path: web/build

  deploy:
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    runs-on: ubuntu-latest
    needs: build
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: Deploy to GitHub Pages
      id: deployment
      uses: actions/deploy-pages@v4