{"name": "dicey-mobile", "version": "1.0.0", "main": "index.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-picker/picker": "2.11.0", "expo": "~53.0.0", "expo-av": "~15.1.4", "expo-haptics": "~14.1.4", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-native": "0.79.2", "react-native-safe-area-context": "5.4.0"}, "devDependencies": {"@babel/core": "^7.25.0", "@types/react": "~19.0.10", "@types/react-native": "^0.72.8", "typescript": "~5.8.3"}, "private": true}