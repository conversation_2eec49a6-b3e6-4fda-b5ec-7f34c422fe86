/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: #f5f5f5;
  color: #333;
}

.app {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

/* Header */
.app-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 2rem;
  text-align: center;
}

.app-header h1 {
  font-size: 2.5rem;
  margin-bottom: 0.5rem;
}

.app-header p {
  opacity: 0.9;
  font-size: 1.1rem;
}

/* Navigation */
.app-nav {
  background: white;
  padding: 0;
  display: flex;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.nav-btn {
  background: none;
  border: none;
  padding: 1rem 2rem;
  cursor: pointer;
  font-size: 1rem;
  border-bottom: 3px solid transparent;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background-color: #f0f0f0;
}

.nav-btn.active {
  border-bottom-color: #667eea;
  color: #667eea;
  font-weight: 600;
}

/* Main content */
.app-main {
  flex: 1;
  padding: 2rem;
}

.content-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.primary-content {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.sidebar {
  background: white;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
  display: flex;
  flex-direction: column;
}

.full-width-content {
  grid-column: 1 / -1;
  background: white;
  border-radius: 12px;
  padding: 2rem;
  box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

/* Dice Builder */
.dice-builder h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.presets-section {
  margin-bottom: 1.5rem;
}

.presets-section h3 {
  margin-bottom: 0.75rem;
  color: #555;
  font-size: 1rem;
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(70px, 1fr));
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.preset-btn {
  font-size: 0.8rem;
  padding: 0.5rem;
  min-width: 60px;
}

.dice-list {
  margin-bottom: 1.5rem;
}

.die-config {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.die-config label {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  font-weight: 500;
}

.die-config input,
.die-config select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.remove-btn:hover {
  background: #c82333;
}

.add-die-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin-bottom: 1.5rem;
  transition: background-color 0.3s ease;
}

.add-die-btn:hover {
  background: #218838;
}

.config-preview {
  background: #e9ecef;
  padding: 1rem;
  border-radius: 6px;
  margin-bottom: 1.5rem;
  text-align: center;
}

.actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.save-section {
  display: flex;
  gap: 1rem;
  align-items: center;
}

.save-section input {
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
}

/* Button group */
.button-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

/* Base button styles */
.btn {
  border: none;
  padding: 0.75rem 1rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.9rem;
  font-weight: 500;
  transition: all 0.3s ease;
  min-width: 80px;
  text-align: center;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #5a6268;
}

.btn-success {
  background: #28a745;
  color: white;
}

.btn-success:hover:not(:disabled) {
  background: #218838;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

/* Buttons */
.roll-btn.primary {
  background: #667eea;
  color: white;
  border: none;
  padding: 1rem 2rem;
  border-radius: 8px;
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.roll-btn.primary:hover:not(:disabled) {
  background: #5a6fd8;
  transform: translateY(-2px);
}

.roll-btn.primary:disabled {
  background: #ccc;
  cursor: not-allowed;
  transform: none;
}

.roll-btn.rolling {
  animation: shake 0.4s ease-in-out infinite;
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-2px); }
  20%, 40%, 60%, 80% { transform: translateX(2px); }
}

.save-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.save-btn:hover:not(:disabled) {
  background: #218838;
}

.save-btn:disabled {
  background: #ccc;
  cursor: not-allowed;
}

.delete-btn.danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.delete-btn.danger:hover {
  background: #c82333;
}

.clear-btn.danger {
  background: #dc3545;
  color: white;
  border: none;
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.clear-btn.danger:hover {
  background: #c82333;
}

/* Saved Configurations */
.saved-configurations h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.configurations-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.configuration-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.config-info h3 {
  margin-bottom: 0.5rem;
  color: #333;
}

.config-formula {
  font-family: 'Courier New', monospace;
  color: #666;
  margin-bottom: 0.5rem;
}

.config-info small {
  color: #999;
}

.config-actions {
  display: flex;
  gap: 0.5rem;
}

/* Roll Result */
.roll-result {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.roll-result .roll-btn.primary {
  margin-bottom: 1rem;
}

.result-header {
  text-align: center;
  margin-bottom: 1rem;
}

.result-header .total {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 0.9rem;
  font-weight: 600;
  color: #666;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.total-value {
  color: #667eea;
  font-size: 2.5rem;
  font-weight: 700;
}

.dice-results {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  margin-bottom: 1rem;
  justify-content: center;
}

.die-group-results {
  display: flex;
  gap: 0.5rem;
  align-items: center;
  flex-wrap: wrap;
}

.roll-value {
  background: #667eea;
  color: white;
  padding: 0.75rem;
  border-radius: 8px;
  font-weight: 600;
  min-width: 44px;
  text-align: center;
  font-size: 1.1rem;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.roll-value.modifier.positive {
  background: #28a745;
}

.roll-value.modifier.negative {
  background: #dc3545;
}

.roll-value.modifier {
  min-width: 50px;
  font-size: 1rem;
}

.roll-timestamp {
  text-align: center;
  color: #999;
  font-size: 0.9rem;
}

/* Roll History */
.roll-history h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.history-item {
  padding: 1.5rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
}

.roll-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.roll-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.roll-total-badge {
  background: #667eea;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 600;
  font-size: 1.1rem;
}

.roll-dice-results {
  margin-bottom: 1rem;
}

.die-group {
  margin-bottom: 1rem;
  padding: 1rem;
  background: white;
  border-radius: 6px;
}

.die-group:last-child {
  margin-bottom: 0;
}

.die-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.die-type {
  font-weight: 600;
  color: #333;
}

.die-sum {
  color: #666;
  font-size: 0.9rem;
}

.individual-rolls {
  display: flex;
  gap: 0.5rem;
  flex-wrap: wrap;
}

.roll-value {
  background: #667eea;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-weight: 600;
  min-width: 30px;
  text-align: center;
  font-size: 0.9rem;
}

.roll-meta {
  color: #666;
  font-size: 0.9rem;
  font-style: italic;
}

/* Compact history styles */
.history-item-compact {
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  border-left: 4px solid #667eea;
  margin-bottom: 1rem;
}

.history-date {
  font-size: 0.9rem;
  color: #666;
  margin-bottom: 0.5rem;
}

.history-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.history-dice {
  display: flex;
  gap: 0.5rem;
  flex: 1;
  justify-content: center;
  flex-wrap: wrap;
}

.history-total {
  font-size: 1.2rem;
  font-weight: bold;
  color: #667eea;
  background: white;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  min-width: 50px;
  text-align: center;
}

/* Empty states */
.empty {
  text-align: center;
  padding: 3rem;
  color: #666;
}

.empty h2 {
  margin-bottom: 1rem;
}

.empty-state {
  text-align: center;
  padding: 2rem;
  color: #666;
}

.empty-state p:first-child {
  font-size: 2rem;
  margin-bottom: 1rem;
}

/* Dice table */
.dice-table-header {
  display: grid;
  gap: 1rem;
  padding: 0.75rem;
  background: #f8f9fa;
  border-radius: 6px;
  font-weight: 600;
  margin-bottom: 1rem;
  text-align: center;
}

/* With modifiers enabled */
.dice-table-header.with-modifiers {
  grid-template-columns: 80px 1fr 120px 60px;
}

/* Without modifiers */
.dice-table-header.without-modifiers {
  grid-template-columns: 80px 1fr 60px;
}

.die-config {
  display: grid;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
  align-items: center;
}

/* With modifiers enabled */
.die-config.with-modifiers {
  grid-template-columns: 80px 1fr 120px 60px;
}

/* Without modifiers */
.die-config.without-modifiers {
  grid-template-columns: 80px 1fr 60px;
}

.die-config input,
.die-config select {
  padding: 0.5rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  text-align: center;
}

.die-config select {
  padding-right: 2rem;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="8" viewBox="0 0 12 8"><path fill="%23666" d="M6 8L0 2h12z"/></svg>');
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  background-size: 12px;
  appearance: none;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  width: 90%;
  max-width: 400px;
}

.modal h3 {
  margin-bottom: 1rem;
  text-align: center;
  color: #333;
}

.modal input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  margin-bottom: 1.5rem;
}

.modal-actions {
  display: flex;
  gap: 1rem;
  justify-content: flex-end;
}

.cancel-btn {
  background: #6c757d;
  color: white;
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.cancel-btn:hover {
  background: #5a6268;
}

/* Statistics */
.statistics h2 {
  margin-bottom: 1.5rem;
  color: #333;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: #f8f9fa;
  padding: 1.5rem;
  border-radius: 8px;
  text-align: center;
  border-left: 4px solid #667eea;
}

.stat-card h3 {
  margin-bottom: 0.5rem;
  color: #666;
  font-size: 0.9rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  font-size: 2rem;
  font-weight: 700;
  color: #667eea;
}

.chart-section {
  margin-bottom: 2rem;
}

.chart-section h3 {
  margin-bottom: 1rem;
  color: #333;
}

.histogram {
  display: flex;
  align-items: flex-end;
  gap: 4px;
  min-height: 250px;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  overflow-x: auto;
}

.histogram-bar {
  display: flex;
  flex-direction: column;
  align-items: center;
  min-width: 40px;
}

.bar {
  background: linear-gradient(to top, #667eea, #764ba2);
  border-radius: 4px 4px 0 0;
  width: 30px;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding-top: 4px;
  transition: all 0.3s ease;
}

.bar:hover {
  background: linear-gradient(to top, #5a6fd8, #6a4194);
}

.bar-count {
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
}

.bar-label {
  margin-top: 8px;
  font-size: 0.8rem;
  color: #666;
  font-weight: 500;
}

.recent-rolls h3 {
  margin-bottom: 1rem;
  color: #333;
}

.trend-line {
  display: flex;
  align-items: flex-end;
  gap: 2px;
  height: 120px;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.trend-point {
  background: #667eea;
  width: 8px;
  border-radius: 4px 4px 0 0;
  transition: all 0.3s ease;
}

.trend-point:hover {
  background: #5a6fd8;
  width: 12px;
}

/* Loading */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  font-size: 1.1rem;
  color: #666;
}

/* Responsive design */
@media (max-width: 768px) {
  .content-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .sidebar {
    position: static;
  }
  
  .app-nav {
    flex-direction: column;
  }
  
  .nav-btn {
    padding: 0.75rem 1rem;
  }
  
  .die-config {
    flex-direction: column;
    align-items: stretch;
  }
  
  .save-section {
    flex-direction: column;
  }
  
  .configuration-item {
    flex-direction: column;
    gap: 1rem;
  }
  
  .config-actions {
    align-self: stretch;
    justify-content: center;
  }
}